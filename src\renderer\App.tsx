/// <reference types="./vite-env.d.ts" />

import { useState, useCallback, useEffect, useRef } from 'react';
import { ImageUpload } from './components/ImageUpload';
import { TextInput } from './components/TextInput';
import { ModelViewer } from './components/ModelViewer';
import { Settings } from './components/Settings';
import { SampleGallery } from './components/SampleGallery';
import { ProgressBar } from './components/ProgressBar';
import { SettingsModal } from './components/SettingsModal';
import { PromptCrafter } from './components/PromptCrafter';
import ProjectsGallery from './components/ProjectsGallery';
import { ImageGeneration } from './components/ImageGeneration';
import MainMenu from './components/MainMenu';

import { AgisoftDelighter } from './components/AgisoftDelighter';
import { Image as ImageIcon, Type as TypeIcon, Cuboid as Cube, Download, Play, Sun, Moon, AlertCircle, Settings as SettingsIcon, FolderOpen } from 'lucide-react';

const electronAPI = (window as any).electronAPI;

interface ModelSettings {
  ss_steps: number;
  ss_cfg_strength: number;
  slat_steps: number;
  slat_cfg_strength: number;
  seed?: number;
  randomize_seed?: boolean;
  simplify: number;
  texture_size: number;
  enable_lighting_optimizer?: boolean;
  // Hunyuan3D-2 specific settings
  octree_resolution?: number;
  num_inference_steps?: number;
  guidance_scale?: number;
  enable_texture?: boolean;
  face_count?: number;
}

interface Pipeline {
  id: string;
  name: string;
  description: string;
  available: boolean;
  features: string[];
}

type InputMode = 'image' | 'text';
type NavigationMode = 'generator' | 'imagegen' | 'projects';

function App() {
  const [navigationMode, setNavigationMode] = useState<NavigationMode>('generator');
  const [inputMode, setInputMode] = useState<InputMode>('image');
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [textPrompt, setTextPrompt] = useState<string>('');
  const [uploadedFile, setUploadedFile] = useState<{ image_id: string; filename: string; bg_removed: boolean; path: string } | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showTextured, setShowTextured] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [modelUrl, setModelUrl] = useState<string | null>(null);
  const [modelFilePath, setModelFilePath] = useState<string | null>(null);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [modelError, setModelError] = useState<string | null>(null);
  const [isLoadingSample, setIsLoadingSample] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [selectedImageModel, setSelectedImageModel] = useState('sdxl_turbo');
  const [settings, setSettings] = useState<ModelSettings>({
    ss_steps: 12,
    ss_cfg_strength: 7.5,
    slat_steps: 12,
    slat_cfg_strength: 3.0,
    randomize_seed: true,
    seed: Math.floor(Math.random() * 1000000),
    simplify: 0.95,
    texture_size: 1024,
    enable_lighting_optimizer: true,
    // Hunyuan3D-2 defaults
    octree_resolution: 128,
    num_inference_steps: 5,
    guidance_scale: 5.0,
    enable_texture: false,
    face_count: 40000
  });

  // Pipeline selection state
  const [selectedPipeline, setSelectedPipeline] = useState('TrellisSource');
  const [availablePipelines, setAvailablePipelines] = useState<Pipeline[]>([]);

  // Agisoft De-Lighter texture enhancement state (disabled by default - advanced feature)
  const [delighterEnabled, setDelighterEnabled] = useState(false);
  const [delighterQuality, setDelighterQuality] = useState('high');
  const [delighterPreviewImage, setDelighterPreviewImage] = useState<string | null>(null);

  // Generation stats for info panel
  const [generationStats, setGenerationStats] = useState<any>(null);

  const projectsGalleryRef = useRef<any>(null);

  const [isUploading, setIsUploading] = useState(false);

  // Load current model preference and available pipelines on component mount
  useEffect(() => {
    const loadModelPreference = async () => {
      try {
        const config = await electronAPI.getConfig('huggingface-token');
        if (config && config.preferred_model) {
          setSelectedImageModel(config.preferred_model);
        }
      } catch (error) {
        console.error('Error loading model preference:', error);
      }
    };

    const loadAvailablePipelines = async () => {
      try {
        const pipelines = await electronAPI.getAvailablePipelines();
        console.log('All pipelines received from backend:', pipelines.map((p: Pipeline) => p.id));
        const allowed = pipelines.filter((p: Pipeline) => ['TrellisSource', 'Microsoft_TRELLIS', 'Hunyaun3d-2'].includes(p.id));
        console.log('Filtered pipelines:', allowed.map((p: Pipeline) => p.id));
        setAvailablePipelines(allowed);

        if (!allowed.find((p: Pipeline) => p.id === selectedPipeline) && allowed.length > 0) {
          setSelectedPipeline(allowed[0].id);
        }
      } catch (error) {
        console.error('Error loading available pipelines:', error);
      }
    };

    loadModelPreference();
    loadAvailablePipelines();
  }, []);

  // Note: Progress tracking is handled by the ProgressBar component
  // No need for duplicate listeners here

  const handleImageUpload = useCallback(async (file: File) => {
    setIsUploading(true);
    setUploadError(null);
    setModelError(null);
    setModelUrl(null);
    setModelFilePath(null);
    setVideoUrl(null);
  
    const reader = new FileReader();
    reader.onload = (e) => {
      setSelectedImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  
    try {
      // Convert file to buffer to send over IPC
      const buffer = await file.arrayBuffer();
      const result = await electronAPI.uploadFile({
        buffer: new Uint8Array(buffer),
        filename: file.name
      });
  
      if (!result || !result.image_id) {
        throw new Error('Failed to upload image via IPC');
      }
  
      // Fetch the processed image (background removed or fallback) for preview
      try {
        const processedUrl = await electronAPI.loadFile(result.path);
        if (processedUrl) {
          setSelectedImage(processedUrl);
        }
      } catch (e) {
        console.warn('Could not load processed image, using original preview', e);
      }
  
      setUploadedFile({
        image_id: result.image_id,
        filename: result.filename,
        bg_removed: result.bg_removed,
        path: result.path,
      } as any);
  
      setDelighterPreviewImage(null);
    } catch (err) {
      console.error('Error uploading image:', err);
      setUploadError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  }, []);

  const handleTextChange = useCallback((prompt: string) => {
    setTextPrompt(prompt);

    // Create or clear the "uploaded file" based on prompt content
    if (prompt.trim()) {
      setUploadedFile({
        image_id: 'text-' + Date.now(),
        filename: 'text-prompt.txt',
        bg_removed: false,
        path: '',
      } as any);
    } else {
      setUploadedFile(null);
    }
  }, []);

  const handlePromptSelect = useCallback((prompt: string) => {
    setTextPrompt(prompt);
    // Also trigger the same logic as handleTextChange
    if (prompt.trim()) {
      setUploadedFile({
        image_id: 'text-' + Date.now(),
        filename: 'text-prompt.txt',
        bg_removed: false,
        path: '',
      } as any);
    } else {
      setUploadedFile(null);
    }
  }, []);

  const handleTextSubmit = useCallback((prompt: string) => {
    setTextPrompt(prompt);
    setUploadError(null);
    setModelError(null);
    setModelUrl(null);
    setModelFilePath(null);
    setVideoUrl(null);

    // Create a mock "uploaded file" for text prompts
    setUploadedFile({
      image_id: 'text-' + Date.now(),
      filename: 'text-prompt.txt',
      bg_removed: false,
      path: '',
    } as any);
  }, []);

  const handleModeSwitch = useCallback((mode: InputMode) => {
    setInputMode(mode);
    // Clear current input when switching modes
    setSelectedImage(null);
    setTextPrompt('');
    setUploadedFile(null);
    setUploadError(null);
    setModelError(null);
    setModelUrl(null);
    setModelFilePath(null);
    setVideoUrl(null);
    setDelighterPreviewImage(null);
  }, []);

  // Agisoft De-Lighter handlers
  const handleDelighterEnabledChange = useCallback((enabled: boolean) => {
    setDelighterEnabled(enabled);
  }, []);

  const handleDelighterQualityChange = useCallback((quality: string) => {
    setDelighterQuality(quality);
  }, []);

  const handleImageModelChange = useCallback(async (model: string) => {
    setSelectedImageModel(model);
    try {
      await electronAPI.setConfig('preferred_model', model);
    } catch (error) {
      console.error('Failed to set model preference:', error);
    }
  }, []);

  const handlePipelineChange = useCallback((pipeline: string) => {
    setSelectedPipeline(pipeline);
  }, []);

  const handleSampleSelect = useCallback(async (sample: { filename: string; prompt: string; url?: string }) => {
    setIsLoadingSample(true);
    setUploadError(null);
    setModelError(null);
    setModelUrl(null);
    setModelFilePath(null);
    setVideoUrl(null);

    try {
      // The sample already contains the data URL from getSampleImages
      if (!sample.url) {
        throw new Error('Sample image data not available.');
      }

      setSelectedImage(sample.url); // The URL is already a data URL
      setTextPrompt(sample.prompt);

      // Create a mock uploaded file from the sample
      setUploadedFile({
        image_id: `sample-${sample.filename}`,
        filename: sample.filename,
        bg_removed: false,
        path: '',
      } as any);

      // Trigger background removal for the sample image
      try {
        const response = await fetch(sample.url);
        const blob = await response.blob();
        const file = new File([blob], sample.filename, { type: blob.type });

        // Process the image for background removal
        await handleImageUpload(file);
      } catch (bgError) {
        console.warn('Background removal failed for sample image:', bgError);
        // Continue without background removal
      }

    } catch (error) {
      console.error('Error loading sample:', error);
      setUploadError('Failed to load sample image.');
    } finally {
      setIsLoadingSample(false);
    }
  }, [handleImageUpload]);

  const handleGenerateModel = useCallback(async () => {
    if (!uploadedFile) return;

    setIsProcessing(true);
    setModelError(null);
    setModelUrl(null);
    setModelFilePath(null);
    setVideoUrl(null);
    setGenerationStats(null);

    const currentSeed = settings.randomize_seed ? Math.floor(Math.random() * 1000000) : settings.seed;

    const generationSettings = {
      ...settings,
      seed: currentSeed,
    };
    
    try {
      const result = await electronAPI.runPipeline(selectedPipeline, {
        input_image_id: uploadedFile.image_id,
        input_image_path: uploadedFile.path,
        text_prompt: textPrompt,
        settings: generationSettings,
        selected_model: selectedImageModel,
      });

      if (!result || !result.success) {
        throw new Error(result.error || 'An unknown error occurred during model generation.');
      }

      // Assuming the backend returns paths that need to be served
      // We need a way to get a file URL from a local path
      const model_url = await electronAPI.loadFile(result.model_path);
      const video_url = await electronAPI.loadFile(result.video_path);

      console.log('Setting model URL:', model_url);
      console.log('Setting model file path:', result.model_path);
      setModelUrl(model_url);
      setModelFilePath(result.model_path);
      setVideoUrl(video_url);

      // For text-to-3D generation, update the uploadedFile with the generated image path
      if (inputMode === 'text' && result.generated_image_path && uploadedFile) {
        console.log('Updating uploadedFile with generated image path:', result.generated_image_path);
        setUploadedFile({
          ...uploadedFile,
          path: result.generated_image_path
        });
      }

      // Save to projects
      if (result.project_id) {
        if(projectsGalleryRef.current) {
          projectsGalleryRef.current.refresh();
        }
      }
      
      setGenerationStats(result.stats || {});
      setShowTextured(true);

    } catch (err: any) {
      console.error('Error generating model:', err);
      setModelError(err.message || 'Failed to generate model. Check logs for details.');
    } finally {
      setIsProcessing(false);
    }
  }, [uploadedFile, textPrompt, settings, selectedPipeline, selectedImageModel]);

  const handleDownloadModel = useCallback(() => {
    if (!modelUrl) return;
    
    // The modelUrl is now a file path from the backend
    // We need to ask the main process to handle the download.
    electronAPI.downloadFile(modelUrl);

  }, [modelUrl]);

  const handleSettingsChange = useCallback((newSettings: Partial<ModelSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  }, []);

  const handleEnhanceTexture = useCallback(async () => {
    if (!modelUrl) return;

    try {
      const result = await electronAPI.runPipeline('agisoft-delighter', {
        model_path: modelUrl,
        quality: delighterQuality
      });
      
      if (result.success && result.preview_image) {
        setDelighterPreviewImage(`data:image/png;base64,${result.preview_image}`);
      } else {
        setModelError('Failed to enhance texture. Check logs.');
      }
    } catch (error: any) {
      console.error('Error enhancing texture:', error);
      setModelError(error.message || 'An error occurred during texture enhancement.');
    }
  }, [modelUrl, delighterQuality]);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    document.documentElement.classList.toggle('dark', !isDarkMode);
  };
  
  const handleProjectSaved = () => {
    if (projectsGalleryRef.current) {
      projectsGalleryRef.current.refresh();
    }
  };

  // Handler to create 3D model from image project
  const handleCreate3DFromImage = useCallback(async (imageUrl: string, prompt?: string) => {
    setNavigationMode('generator');
    setInputMode('image');
    setTextPrompt(prompt || '');
    setSelectedImage(imageUrl);
    setModelUrl(null);
    setModelFilePath(null);
    setVideoUrl(null);
    setUploadError(null);
    setModelError(null);

    // Trigger background removal for the selected image
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const file = new File([blob], 'project-image.png', { type: 'image/png' });
      
      // Use the existing handleImageUpload function to process the image
      await handleImageUpload(file);
    } catch (err) {
      console.error('Error processing project image:', err);
      setUploadError('Failed to process image. Please try again.');
    }
  }, [handleImageUpload]);

  return (
    <div className={`h-screen overflow-hidden ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      <MainMenu isDarkMode={isDarkMode} toggleTheme={toggleTheme} />
      <header className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            {/* Navigation Menu */}
            <div className="flex items-center gap-8">
              {/* Logo/Brand - Now clickable */}
              <button
                onClick={() => setNavigationMode('generator')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                  navigationMode === 'generator'
                    ? isDarkMode
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-blue-600 text-white shadow-lg'
                    : isDarkMode
                    ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                    : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <Cube className="w-6 h-6" />
                <span className="text-lg font-bold">3D Model Generator</span>
              </button>
              {/* Image Generation Menu Item */}
              <button
                onClick={() => setNavigationMode('imagegen')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                  navigationMode === 'imagegen'
                    ? isDarkMode
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-blue-600 text-white shadow-lg'
                    : isDarkMode
                    ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                    : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <ImageIcon className="w-5 h-5" />
                <span className="font-medium">Image Generation</span>
              </button>
              {/* Projects Menu Item */}
              <button
                onClick={() => setNavigationMode('projects')}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                  navigationMode === 'projects'
                    ? isDarkMode
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-blue-600 text-white shadow-lg'
                    : isDarkMode
                    ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                    : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <FolderOpen className="w-5 h-5" />
                <span className="font-medium">Projects</span>
              </button>
            </div>

            {/* Right Side Controls */}
            <div className="flex items-center gap-2">
            </div>
          </div>
        </div>
      </header>

      <main className="h-[calc(100vh-80px)] px-4 py-8 sm:px-6 lg:px-8 overflow-auto">
        {navigationMode === 'imagegen' ? (
          <ImageGeneration isDarkMode={isDarkMode} onProjectSaved={handleProjectSaved} onSaveAsProject={() => {}} />
        ) : navigationMode === 'projects' ? (
          <ProjectsGallery ref={projectsGalleryRef} isDarkMode={isDarkMode} onCreate3DFromImage={handleCreate3DFromImage} />
        ) : navigationMode === 'generator' ? (
          <div className="h-full flex flex-col lg:flex-row gap-6 max-w-full mx-auto">
          {/* Left Panel - Fixed Width */}
          <div className="w-full lg:w-80 lg:flex-shrink-0 space-y-6">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg`}>
              {/* Mode Toggle */}
              <div className="flex items-center gap-2 mb-4">
                <div className={`flex rounded-lg p-1 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <button
                    onClick={() => handleModeSwitch('image')}
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      inputMode === 'image'
                        ? isDarkMode
                          ? 'bg-blue-500 text-white'
                          : 'bg-blue-600 text-white'
                        : isDarkMode
                        ? 'text-gray-300 hover:text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <ImageIcon className="w-4 h-4" />
                    Image
                  </button>
                  <button
                    onClick={() => handleModeSwitch('text')}
                    className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      inputMode === 'text'
                        ? isDarkMode
                          ? 'bg-blue-500 text-white'
                          : 'bg-blue-600 text-white'
                        : isDarkMode
                        ? 'text-gray-300 hover:text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <TypeIcon className="w-4 h-4" />
                    Text
                  </button>
                </div>
              </div>

              {/* Dynamic Header */}
              <div className="flex items-center gap-2 mb-4">
                {inputMode === 'image' ? (
                  <ImageIcon className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
                ) : (
                  <TypeIcon className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
                )}
                <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {inputMode === 'image' ? 'Input Image' : 'Text Prompt'}
                </h2>
              </div>
              {/* Conditional Input Area */}
              {inputMode === 'image' ? (
                selectedImage ? (
                  <div className="relative">
                    <img
                      src={delighterPreviewImage || selectedImage}
                      alt="Selected"
                      className="w-full h-[400px] object-contain rounded-lg"
                    />
                    {(uploadedFile as any)?.bg_removed && !isUploading && (
                      <div className="absolute bottom-2 left-2 bg-green-600 text-white px-2 py-1 rounded text-xs font-medium">
                        Background Removed
                      </div>
                    )}
                    {isUploading && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
                        <div className="text-white text-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                          <p>Removing background...</p>
                        </div>
                      </div>
                    )}
                    {isLoadingSample && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
                        <div className="text-white text-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                          <p>Processing sample image...</p>
                        </div>
                      </div>
                    )}
                    <button
                      onClick={() => {
                        setSelectedImage(null);
                        setUploadedFile(null);
                        setUploadError(null);
                        setDelighterPreviewImage(null);
                      }}
                      className={`absolute top-2 right-2 ${
                        isDarkMode ? 'bg-gray-700' : 'bg-white'
                      } rounded-full p-1 shadow-lg`}
                    >
                      ×
                    </button>
                  </div>
                ) : (
                  <ImageUpload onImageUpload={handleImageUpload} isDarkMode={isDarkMode} disabled={isUploading} />
                )
              ) : (
                <TextInput
                  onTextSubmit={handleTextSubmit}
                  onTextChange={handleTextChange}
                  isDarkMode={isDarkMode}
                  isProcessing={isProcessing}
                  selectedModel={selectedImageModel}
                  onModelChange={handleImageModelChange}
                />
              )}
              {uploadError && (
                <div className={`mt-2 p-2 rounded ${isDarkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-700'}`}>
                  <p className="text-sm">{uploadError}</p>
                </div>
              )}
            </div>


            <Settings
              onSettingsChange={setSettings}
              isDarkMode={isDarkMode}
              initialSettings={settings}
              selectedPipeline={selectedPipeline}
              onPipelineChange={handlePipelineChange}
              availablePipelines={availablePipelines}
              selectedModel={selectedImageModel}
              onModelChange={handleImageModelChange}
            />

            <div className="flex gap-4">
              <button
                onClick={handleGenerateModel}
                disabled={
                  (inputMode === 'image' && !selectedImage) ||
                  (inputMode === 'text' && !textPrompt.trim()) ||
                  isProcessing
                }
                className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-lg text-white ${
                  (inputMode === 'image' && !selectedImage) ||
                  (inputMode === 'text' && !textPrompt.trim()) ||
                  isProcessing
                    ? 'bg-gray-400'
                    : isDarkMode
                    ? 'bg-blue-500 hover:bg-blue-600'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                <Play className="w-5 h-5" />
                {isProcessing ? 'Processing...' : 'Generate Model'}
              </button>

              <button
                onClick={handleDownloadModel}
                disabled={!modelUrl}
                className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${
                  modelUrl
                    ? isDarkMode
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-blue-500 hover:bg-blue-600 text-white'
                    : isDarkMode
                    ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                }`}
              >
                <Download className="w-4 h-4 mr-2" />
                Download Model
              </button>
            </div>
          </div>

          {/* Center Panel - Flexible Width */}
          <div className="flex-1 min-w-0 flex flex-col gap-6">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg flex-1 flex flex-col min-h-0`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Cube className={`w-5 h-5 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`} />
                  <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>3D Preview</h2>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setShowTextured(true)}
                    className={`px-3 py-1 rounded ${
                      showTextured
                        ? isDarkMode
                          ? 'bg-blue-500 text-white'
                          : 'bg-blue-600 text-white'
                        : isDarkMode
                        ? 'bg-gray-700 text-gray-300'
                        : 'bg-gray-100 text-gray-700'
                    }`}
                  >
                    Textured
                  </button>
                  <button
                    onClick={() => setShowTextured(false)}
                    className={`px-3 py-1 rounded ${
                      !showTextured
                        ? isDarkMode
                          ? 'bg-blue-500 text-white'
                          : 'bg-blue-600 text-white'
                        : isDarkMode
                        ? 'bg-gray-700 text-gray-300'
                        : 'bg-gray-100 text-gray-700'
                    }`}
                  >
                    Wireframe
                  </button>
                </div>
              </div>
              <div className="flex-1 min-h-[400px]">
                {modelError ? (
                  <div className={`flex flex-col items-center justify-center h-full ${isDarkMode ? 'text-red-400' : 'text-red-600'}`}>
                    <AlertCircle className="w-12 h-12 mb-4" />
                    <p className="text-center">{modelError}</p>
                  </div>
                ) : modelUrl ? (
                  <ModelViewer
                    isTextured={showTextured}
                    isDarkMode={isDarkMode}
                    modelUrl={modelUrl}
                    filePath={modelFilePath}
                    generationStats={generationStats}
                    originalImagePath={uploadedFile?.path}
                    prompt={textPrompt}
                    settings={settings}
                    onProjectSaved={() => {
                      // Refresh projects gallery after saving
                      if (projectsGalleryRef.current) {
                        projectsGalleryRef.current.refresh();
                      }
                    }}
                  />
                ) : videoUrl ? (
                  <video
                    src={videoUrl}
                    className="w-full h-full object-contain"
                    autoPlay
                    loop
                    muted
                    controls
                  />
                ) : (
                  <ModelViewer
                    isTextured={showTextured}
                    isDarkMode={isDarkMode}
                    modelUrl={null}
                    generationStats={generationStats}
                    originalImagePath={uploadedFile?.path}
                    prompt={textPrompt}
                    settings={settings}
                    onProjectSaved={() => {
                      // Refresh projects gallery after saving
                      if (projectsGalleryRef.current) {
                        projectsGalleryRef.current.refresh();
                      }
                    }}
                  />
                )}
              </div>
            </div>

            {/* Progress Bar - Compact when visible */}
            {isProcessing && (
              <div className="flex-shrink-0">
                <ProgressBar
                  isVisible={isProcessing}
                  isDarkMode={isDarkMode}
                  sessionId={uploadedFile?.image_id}
                  generationMode={inputMode === 'image' ? 'image-to-3d' : 'text-to-3d'}
                  delighterEnabled={delighterEnabled}
                  onComplete={() => {
                    console.log('Generation completed!');
                  }}
                  onError={(error) => {
                    console.error('Generation error:', error);
                    setModelError(error);
                    setIsProcessing(false);
                  }}
                />
              </div>
            )}
          </div>

          {/* Right Panel - Fixed Width */}
          <div className="w-full lg:w-96 lg:flex-shrink-0 space-y-6">
            {inputMode === 'image' ? (
              <SampleGallery
                onSampleSelect={handleSampleSelect}
                isDarkMode={isDarkMode}
              />
            ) : inputMode === 'text' ? (
              <PromptCrafter
                onPromptSelect={handlePromptSelect}
                isDarkMode={isDarkMode}
                isProcessing={isProcessing}
              />
            ) : null}
          </div>
        </div>
        ) : null}
      </main>

      {/* Settings Modal (App-level) */}
      <SettingsModal
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        isDarkMode={isDarkMode}
      />
    </div>
  );
}

export default App;